# Redis Sentinel CRUD Demo

这是一个基于Spring Boot的Redis Sentinel集群连接和CRUD操作演示项目。

## 功能特性

- Redis Sentinel集群自动故障转移
- 完整的CRUD操作（创建、读取、更新、删除）
- 用户搜索功能
- 批量操作
- 缓存过期时间管理
- RESTful API接口

## 技术栈

- Spring Boot 3.5.5
- Redis Sentinel
- Jedis客户端
- Jackson JSON序列化

## 项目结构

```
src/main/java/com/trinasloar/gateway/zeuspaasyz/
├── config/
│   └── RedisConfig.java          # Redis Sentinel配置类
├── controller/
│   └── UserController.java       # REST API控制器
├── entity/
│   └── User.java                 # 用户实体类
├── service/
│   └── RedisService.java         # Redis服务类
└── ZeusPaasYzApplication.java    # Spring Boot启动类
```

## 配置说明

### 1. Redis Sentinel配置

在 `application.properties` 中配置Redis Sentinel相关参数：

```properties
# Redis Sentinel Configuration
spring.redis.sentinel.master=mymaster                    # Sentinel主节点名称
spring.redis.sentinel.nodes=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381  # Sentinel节点列表
spring.redis.password=your_password_here                # Redis密码（如果有）
spring.redis.database=0                                 # Redis数据库索引
spring.redis.timeout=2000ms                             # 连接超时时间

# Redis Connection Pool
spring.redis.jedis.pool.max-active=8                    # 最大活跃连接数
spring.redis.jedis.pool.max-idle=8                      # 最大空闲连接数
spring.redis.jedis.pool.min-idle=0                      # 最小空闲连接数
spring.redis.jedis.pool.max-wait=-1ms                   # 最大等待时间
```

### 2. Redis Sentinel集群搭建

确保你的Redis Sentinel集群已经正确配置和运行。你需要：

1. 启动多个Redis实例（主从模式）
2. 配置Sentinel监控这些Redis实例
3. 确保Sentinel节点能够相互通信

## API接口文档

### 创建用户
```http
POST /api/users
Content-Type: application/json

{
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "age": 30,
    "address": "北京市朝阳区"
}
```

### 获取用户
```http
GET /api/users/{id}
```

### 更新用户
```http
PUT /api/users/{id}
Content-Type: application/json

{
    "username": "john_updated",
    "email": "<EMAIL>",
    "age": 31
}
```

### 删除用户
```http
DELETE /api/users/{id}
```

### 获取所有用户
```http
GET /api/users
```

### 搜索用户
```http
GET /api/users/search?username=john
```

### 批量创建用户
```http
POST /api/users/batch
Content-Type: application/json

[
    {
        "id": 2,
        "username": "jane_doe",
        "email": "<EMAIL>"
    },
    {
        "id": 3,
        "username": "bob_smith",
        "email": "<EMAIL>"
    }
]
```

### 设置用户过期时间
```http
POST /api/users/{id}/expire?days=7
```

### 获取用户过期时间
```http
GET /api/users/{id}/expire
```

### 检查用户是否存在
```http
GET /api/users/{id}/exists
```

### 获取用户统计信息
```http
GET /api/users/stats
```

## 运行项目

1. 确保Redis Sentinel集群正常运行
2. 修改 `application.properties` 中的Sentinel配置
3. 运行Spring Boot应用：

```bash
./mvnw spring-boot:run
```

或者

```bash
mvn spring-boot:run
```

## 测试示例

### 使用curl测试

1. 创建用户：
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "age": 25
  }'
```

2. 获取用户：
```bash
curl http://localhost:8080/api/users/1
```

3. 更新用户：
```bash
curl -X PUT http://localhost:8080/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{
    "username": "updateduser",
    "age": 26
  }'
```

4. 删除用户：
```bash
curl -X DELETE http://localhost:8080/api/users/1
```

### 使用Postman测试

导入以下请求到Postman：

- Method: POST, URL: `http://localhost:8080/api/users`
- Method: GET, URL: `http://localhost:8080/api/users/1`
- Method: PUT, URL: `http://localhost:8080/api/users/1`
- Method: DELETE, URL: `http://localhost:8080/api/users/1`
- Method: GET, URL: `http://localhost:8080/api/users`

## 注意事项

1. **密码配置**：如果Redis集群没有密码保护，请删除 `spring.redis.password` 配置或设置为空值
2. **网络连接**：确保应用服务器能够访问到所有的Sentinel节点
3. **防火墙**：确保Redis端口（6379）和Sentinel端口（26379等）没有被防火墙阻止
4. **序列化**：使用了JSON序列化方式存储对象，确保对象实现了Serializable接口
5. **过期时间**：默认设置了7天的过期时间，可以通过API动态修改

## 故障排除

### 常见错误

1. **Connection refused**: 检查Redis Sentinel节点地址和端口是否正确
2. **Authentication failed**: 检查Redis密码配置
3. **No reachable node**: 确保至少有一个Sentinel节点可访问
4. **Master not found**: 检查Sentinel配置中的master名称是否正确

### 日志调试

启用DEBUG日志查看详细的连接信息：

```properties
logging.level.org.springframework.data.redis=DEBUG
logging.level.redis.clients.jedis=DEBUG
```

## 扩展功能

你可以基于这个项目进行以下扩展：

1. 添加更多实体类和对应的CRUD操作
2. 实现Redis发布订阅功能
3. 添加Redis缓存注解支持
4. 实现分布式锁功能
5. 添加监控和健康检查端点

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
