package com.trinasloar.gateway.zeuspaasyz.service;

import com.trinasloar.gateway.zeuspaasyz.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class RedisService {

    private static final String USER_KEY_PREFIX = "user:";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 创建用户
     * @param user 用户对象
     * @return 是否成功
     */
    public boolean createUser(User user) {
        try {
            String key = USER_KEY_PREFIX + user.getId();
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            redisTemplate.opsForValue().set(key, user);
            // 设置过期时间（可选，7天）
            redisTemplate.expire(key, 7, TimeUnit.DAYS);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据ID获取用户
     * @param id 用户ID
     * @return 用户对象
     */
    public User getUserById(Long id) {
        try {
            String key = USER_KEY_PREFIX + id;
            return (User) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 更新用户
     * @param user 用户对象
     * @return 是否成功
     */
    public boolean updateUser(User user) {
        try {
            String key = USER_KEY_PREFIX + user.getId();
            user.setUpdateTime(LocalDateTime.now());
            redisTemplate.opsForValue().set(key, user);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除用户
     * @param id 用户ID
     * @return 是否成功
     */
    public boolean deleteUser(Long id) {
        try {
            String key = USER_KEY_PREFIX + id;
            return redisTemplate.delete(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取所有用户
     * @return 用户列表
     */
    public List<User> getAllUsers() {
        try {
            Set<String> keys = redisTemplate.keys(USER_KEY_PREFIX + "*");
            List<User> users = new ArrayList<>();

            for (String key : keys) {
                User user = (User) redisTemplate.opsForValue().get(key);
                if (user != null) {
                    users.add(user);
                }
            }

            return users;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 根据用户名搜索用户
     * @param username 用户名
     * @return 用户列表
     */
    public List<User> searchUsersByUsername(String username) {
        try {
            List<User> allUsers = getAllUsers();
            List<User> result = new ArrayList<>();

            for (User user : allUsers) {
                if (user.getUsername() != null &&
                    user.getUsername().toLowerCase().contains(username.toLowerCase())) {
                    result.add(user);
                }
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 批量创建用户
     * @param users 用户列表
     * @return 成功创建的数量
     */
    public int batchCreateUsers(List<User> users) {
        try {
            int successCount = 0;
            for (User user : users) {
                if (createUser(user)) {
                    successCount++;
                }
            }
            return successCount;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 设置用户缓存过期时间
     * @param id 用户ID
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     * @return 是否成功
     */
    public boolean setUserExpire(Long id, long timeout, TimeUnit timeUnit) {
        try {
            String key = USER_KEY_PREFIX + id;
            return redisTemplate.expire(key, timeout, timeUnit);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取用户剩余过期时间
     * @param id 用户ID
     * @return 剩余过期时间（秒）
     */
    public Long getUserExpire(Long id) {
        try {
            String key = USER_KEY_PREFIX + id;
            return redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            e.printStackTrace();
            return -2L; // -2表示key不存在，-1表示永久
        }
    }

    /**
     * 检查用户是否存在
     * @param id 用户ID
     * @return 是否存在
     */
    public boolean userExists(Long id) {
        try {
            String key = USER_KEY_PREFIX + id;
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取用户数量
     * @return 用户总数
     */
    public long getUserCount() {
        try {
            Set<String> keys = redisTemplate.keys(USER_KEY_PREFIX + "*");
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
