package com.trinasloar.gateway.zeuspaasyz.controller;

import com.trinasloar.gateway.zeuspaasyz.entity.User;
import com.trinasloar.gateway.zeuspaasyz.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private RedisService redisService;

    /**
     * 创建用户
     * POST /api/users
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createUser(@RequestBody User user) {
        Map<String, Object> response = new HashMap<>();

        if (user.getId() == null) {
            response.put("success", false);
            response.put("message", "用户ID不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        boolean success = redisService.createUser(user);
        if (success) {
            response.put("success", true);
            response.put("message", "用户创建成功");
            response.put("data", user);
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "用户创建失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取用户
     * GET /api/users/{id}
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getUser(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        User user = redisService.getUserById(id);
        if (user != null) {
            response.put("success", true);
            response.put("data", user);
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "用户不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }

    /**
     * 更新用户
     * PUT /api/users/{id}
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateUser(@PathVariable Long id, @RequestBody User user) {
        Map<String, Object> response = new HashMap<>();

        user.setId(id);
        boolean success = redisService.updateUser(user);

        if (success) {
            response.put("success", true);
            response.put("message", "用户更新成功");
            response.put("data", user);
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "用户更新失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除用户
     * DELETE /api/users/{id}
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteUser(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        boolean success = redisService.deleteUser(id);
        if (success) {
            response.put("success", true);
            response.put("message", "用户删除成功");
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "用户删除失败或用户不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }

    /**
     * 获取所有用户
     * GET /api/users
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllUsers() {
        Map<String, Object> response = new HashMap<>();

        List<User> users = redisService.getAllUsers();
        response.put("success", true);
        response.put("data", users);
        response.put("count", users.size());

        return ResponseEntity.ok(response);
    }

    /**
     * 根据用户名搜索用户
     * GET /api/users/search?username={username}
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchUsers(@RequestParam String username) {
        Map<String, Object> response = new HashMap<>();

        List<User> users = redisService.searchUsersByUsername(username);
        response.put("success", true);
        response.put("data", users);
        response.put("count", users.size());

        return ResponseEntity.ok(response);
    }

    /**
     * 批量创建用户
     * POST /api/users/batch
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchCreateUsers(@RequestBody List<User> users) {
        Map<String, Object> response = new HashMap<>();

        int successCount = redisService.batchCreateUsers(users);
        response.put("success", true);
        response.put("message", "批量创建完成");
        response.put("total", users.size());
        response.put("successCount", successCount);
        response.put("failCount", users.size() - successCount);

        return ResponseEntity.ok(response);
    }

    /**
     * 设置用户过期时间
     * POST /api/users/{id}/expire
     */
    @PostMapping("/{id}/expire")
    public ResponseEntity<Map<String, Object>> setUserExpire(
            @PathVariable Long id,
            @RequestParam(defaultValue = "7") long days) {
        Map<String, Object> response = new HashMap<>();

        boolean success = redisService.setUserExpire(id, days, TimeUnit.DAYS);
        if (success) {
            response.put("success", true);
            response.put("message", "过期时间设置成功");
            response.put("expireDays", days);
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "设置过期时间失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取用户过期时间
     * GET /api/users/{id}/expire
     */
    @GetMapping("/{id}/expire")
    public ResponseEntity<Map<String, Object>> getUserExpire(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        Long expireSeconds = redisService.getUserExpire(id);
        response.put("success", true);
        response.put("expireSeconds", expireSeconds);

        if (expireSeconds == -2) {
            response.put("message", "用户不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } else if (expireSeconds == -1) {
            response.put("message", "用户永久有效");
        } else {
            response.put("message", "剩余过期时间");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 检查用户是否存在
     * GET /api/users/{id}/exists
     */
    @GetMapping("/{id}/exists")
    public ResponseEntity<Map<String, Object>> userExists(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        boolean exists = redisService.userExists(id);
        response.put("success", true);
        response.put("exists", exists);

        return ResponseEntity.ok(response);
    }

    /**
     * 获取用户统计信息
     * GET /api/users/stats
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getUserStats() {
        Map<String, Object> response = new HashMap<>();

        long count = redisService.getUserCount();
        response.put("success", true);
        response.put("userCount", count);

        return ResponseEntity.ok(response);
    }
}
